/// 市场类型（用于账户分类）
enum MarketCategory {
  cnStocks(1, 'cnStocks'), // A股
  hkStocks(2, 'hkStocks'), // 港股
  usStocks(3, 'usStocks'), // 美股
  stockIndex(4, 'stockIndex'), // 股指
  cnFutures(5, 'futures.markets.china'); // 国内期货

  /// 接口 code，如：CN、HK、US
  final int code; // dataType	数据类型 1:A股 2：港股 3：美股 4：股指 5：国内期货

  /// 用于多语言翻译的 key（传给 tr()）
  final String nameKey;

  const MarketCategory(this.code, this.nameKey);
}

