import 'package:gp_stock_app/generated/json/base/json_convert_content.dart';
import 'package:gp_stock_app/core/models/entities/contract_summary_page_entity.dart';

ContractSummaryPageEntity $ContractSummaryPageEntityFromJson(Map<String, dynamic> json) {
  final ContractSummaryPageEntity contractSummaryPageEntity = ContractSummaryPageEntity();
  final int? current = jsonConvert.convert<int>(json['current']);
  if (current != null) {
    contractSummaryPageEntity.current = current;
  }
  final bool? hasNext = jsonConvert.convert<bool>(json['hasNext']);
  if (hasNext != null) {
    contractSummaryPageEntity.hasNext = hasNext;
  }
  final List<ContractSummaryPageRecord>? records = (json['records'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractSummaryPageRecord>(e) as ContractSummaryPageRecord).toList();
  if (records != null) {
    contractSummaryPageEntity.records = records;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    contractSummaryPageEntity.total = total;
  }
  return contractSummaryPageEntity;
}

Map<String, dynamic> $ContractSummaryPageEntityToJson(ContractSummaryPageEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['current'] = entity.current;
  data['hasNext'] = entity.hasNext;
  data['records'] = entity.records.map((v) => v.toJson()).toList();
  data['total'] = entity.total;
  return data;
}

extension ContractSummaryPageEntityExtension on ContractSummaryPageEntity {
  ContractSummaryPageEntity copyWith({
    int? current,
    bool? hasNext,
    List<ContractSummaryPageRecord>? records,
    int? total,
  }) {
    return ContractSummaryPageEntity()
      ..current = current ?? this.current
      ..hasNext = hasNext ?? this.hasNext
      ..records = records ?? this.records
      ..total = total ?? this.total;
  }
}

ContractSummaryPageRecord $ContractSummaryPageRecordFromJson(Map<String, dynamic> json) {
  final ContractSummaryPageRecord contractSummaryPageRecord = ContractSummaryPageRecord();
  final double? accountWinAmount = jsonConvert.convert<double>(json['accountWinAmount']);
  if (accountWinAmount != null) {
    contractSummaryPageRecord.accountWinAmount = accountWinAmount;
  }
  final double? allAsset = jsonConvert.convert<double>(json['allAsset']);
  if (allAsset != null) {
    contractSummaryPageRecord.allAsset = allAsset;
  }
  final double? closeRemindAmount = jsonConvert.convert<double>(json['closeRemindAmount']);
  if (closeRemindAmount != null) {
    contractSummaryPageRecord.closeRemindAmount = closeRemindAmount;
  }
  final double? contractAssetAmount = jsonConvert.convert<double>(json['contractAssetAmount']);
  if (contractAssetAmount != null) {
    contractSummaryPageRecord.contractAssetAmount = contractAssetAmount;
  }
  final double? coverLossAmount = jsonConvert.convert<double>(json['coverLossAmount']);
  if (coverLossAmount != null) {
    contractSummaryPageRecord.coverLossAmount = coverLossAmount;
  }
  final String? currency = jsonConvert.convert<String>(json['currency']);
  if (currency != null) {
    contractSummaryPageRecord.currency = currency;
  }
  final double? expendAmount = jsonConvert.convert<double>(json['expendAmount']);
  if (expendAmount != null) {
    contractSummaryPageRecord.expendAmount = expendAmount;
  }
  final String? expireTime = jsonConvert.convert<String>(json['expireTime']);
  if (expireTime != null) {
    contractSummaryPageRecord.expireTime = expireTime;
  }
  final double? freezePower = jsonConvert.convert<double>(json['freezePower']);
  if (freezePower != null) {
    contractSummaryPageRecord.freezePower = freezePower;
  }
  final double? gapCloseRemindAmount = jsonConvert.convert<double>(json['gapCloseRemindAmount']);
  if (gapCloseRemindAmount != null) {
    contractSummaryPageRecord.gapCloseRemindAmount = gapCloseRemindAmount;
  }
  final double? gapWarnRemindAmount = jsonConvert.convert<double>(json['gapWarnRemindAmount']);
  if (gapWarnRemindAmount != null) {
    contractSummaryPageRecord.gapWarnRemindAmount = gapWarnRemindAmount;
  }
  final double? giveAmount = jsonConvert.convert<double>(json['giveAmount']);
  if (giveAmount != null) {
    contractSummaryPageRecord.giveAmount = giveAmount;
  }
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    contractSummaryPageRecord.id = id;
  }
  final double? initCash = jsonConvert.convert<double>(json['initCash']);
  if (initCash != null) {
    contractSummaryPageRecord.initCash = initCash;
  }
  final double? interestAmount = jsonConvert.convert<double>(json['interestAmount']);
  if (interestAmount != null) {
    contractSummaryPageRecord.interestAmount = interestAmount;
  }
  final double? interestRate = jsonConvert.convert<double>(json['interestRate']);
  if (interestRate != null) {
    contractSummaryPageRecord.interestRate = interestRate;
  }
  final String? marketType = jsonConvert.convert<String>(json['marketType']);
  if (marketType != null) {
    contractSummaryPageRecord.marketType = marketType;
  }
  final int? multiple = jsonConvert.convert<int>(json['multiple']);
  if (multiple != null) {
    contractSummaryPageRecord.multiple = multiple;
  }
  final double? negativeAmount = jsonConvert.convert<double>(json['negativeAmount']);
  if (negativeAmount != null) {
    contractSummaryPageRecord.negativeAmount = negativeAmount;
  }
  final String? openTime = jsonConvert.convert<String>(json['openTime']);
  if (openTime != null) {
    contractSummaryPageRecord.openTime = openTime;
  }
  final int? periodType = jsonConvert.convert<int>(json['periodType']);
  if (periodType != null) {
    contractSummaryPageRecord.periodType = periodType;
  }
  final double? positionAmount = jsonConvert.convert<double>(json['positionAmount']);
  if (positionAmount != null) {
    contractSummaryPageRecord.positionAmount = positionAmount;
  }
  final double? receivableInterest = jsonConvert.convert<double>(json['receivableInterest']);
  if (receivableInterest != null) {
    contractSummaryPageRecord.receivableInterest = receivableInterest;
  }
  final int? settlementStatus = jsonConvert.convert<int>(json['settlementStatus']);
  if (settlementStatus != null) {
    contractSummaryPageRecord.settlementStatus = settlementStatus;
  }
  final double? todayWinAmount = jsonConvert.convert<double>(json['todayWinAmount']);
  if (todayWinAmount != null) {
    contractSummaryPageRecord.todayWinAmount = todayWinAmount;
  }
  final double? todayWinRate = jsonConvert.convert<double>(json['todayWinRate']);
  if (todayWinRate != null) {
    contractSummaryPageRecord.todayWinRate = todayWinRate;
  }
  final int? totalAccountAmount = jsonConvert.convert<int>(json['totalAccountAmount']);
  if (totalAccountAmount != null) {
    contractSummaryPageRecord.totalAccountAmount = totalAccountAmount;
  }
  final double? totalCash = jsonConvert.convert<double>(json['totalCash']);
  if (totalCash != null) {
    contractSummaryPageRecord.totalCash = totalCash;
  }
  final double? totalFinance = jsonConvert.convert<double>(json['totalFinance']);
  if (totalFinance != null) {
    contractSummaryPageRecord.totalFinance = totalFinance;
  }
  final double? totalPower = jsonConvert.convert<double>(json['totalPower']);
  if (totalPower != null) {
    contractSummaryPageRecord.totalPower = totalPower;
  }
  final int? type = jsonConvert.convert<int>(json['type']);
  if (type != null) {
    contractSummaryPageRecord.type = type;
  }
  final double? useAmount = jsonConvert.convert<double>(json['useAmount']);
  if (useAmount != null) {
    contractSummaryPageRecord.useAmount = useAmount;
  }
  final double? warnRemindAmount = jsonConvert.convert<double>(json['warnRemindAmount']);
  if (warnRemindAmount != null) {
    contractSummaryPageRecord.warnRemindAmount = warnRemindAmount;
  }
  final double? winRate = jsonConvert.convert<double>(json['winRate']);
  if (winRate != null) {
    contractSummaryPageRecord.winRate = winRate;
  }
  final double? winAmount = jsonConvert.convert<double>(json['winAmount']);
  if (winAmount != null) {
    contractSummaryPageRecord.winAmount = winAmount;
  }
  final double? withdrawAmount = jsonConvert.convert<double>(json['withdrawAmount']);
  if (withdrawAmount != null) {
    contractSummaryPageRecord.withdrawAmount = withdrawAmount;
  }
  final int? yesterdayAsset = jsonConvert.convert<int>(json['yesterdayAsset']);
  if (yesterdayAsset != null) {
    contractSummaryPageRecord.yesterdayAsset = yesterdayAsset;
  }
  final bool? isAutoRenew = jsonConvert.convert<bool>(json['isAutoRenew']);
  if (isAutoRenew != null) {
    contractSummaryPageRecord.isAutoRenew = isAutoRenew;
  }
  return contractSummaryPageRecord;
}

Map<String, dynamic> $ContractSummaryPageRecordToJson(ContractSummaryPageRecord entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['accountWinAmount'] = entity.accountWinAmount;
  data['allAsset'] = entity.allAsset;
  data['closeRemindAmount'] = entity.closeRemindAmount;
  data['contractAssetAmount'] = entity.contractAssetAmount;
  data['coverLossAmount'] = entity.coverLossAmount;
  data['currency'] = entity.currency;
  data['expendAmount'] = entity.expendAmount;
  data['expireTime'] = entity.expireTime;
  data['freezePower'] = entity.freezePower;
  data['gapCloseRemindAmount'] = entity.gapCloseRemindAmount;
  data['gapWarnRemindAmount'] = entity.gapWarnRemindAmount;
  data['giveAmount'] = entity.giveAmount;
  data['id'] = entity.id;
  data['initCash'] = entity.initCash;
  data['interestAmount'] = entity.interestAmount;
  data['interestRate'] = entity.interestRate;
  data['marketType'] = entity.marketType;
  data['multiple'] = entity.multiple;
  data['negativeAmount'] = entity.negativeAmount;
  data['openTime'] = entity.openTime;
  data['periodType'] = entity.periodType;
  data['positionAmount'] = entity.positionAmount;
  data['receivableInterest'] = entity.receivableInterest;
  data['settlementStatus'] = entity.settlementStatus;
  data['todayWinAmount'] = entity.todayWinAmount;
  data['todayWinRate'] = entity.todayWinRate;
  data['totalAccountAmount'] = entity.totalAccountAmount;
  data['totalCash'] = entity.totalCash;
  data['totalFinance'] = entity.totalFinance;
  data['totalPower'] = entity.totalPower;
  data['type'] = entity.type;
  data['useAmount'] = entity.useAmount;
  data['warnRemindAmount'] = entity.warnRemindAmount;
  data['winRate'] = entity.winRate;
  data['winAmount'] = entity.winAmount;
  data['withdrawAmount'] = entity.withdrawAmount;
  data['yesterdayAsset'] = entity.yesterdayAsset;
  data['isAutoRenew'] = entity.isAutoRenew;
  return data;
}

extension ContractSummaryPageRecordExtension on ContractSummaryPageRecord {
  ContractSummaryPageRecord copyWith({
    double? accountWinAmount,
    double? allAsset,
    double? closeRemindAmount,
    double? contractAssetAmount,
    double? coverLossAmount,
    String? currency,
    double? expendAmount,
    String? expireTime,
    double? freezePower,
    double? gapCloseRemindAmount,
    double? gapWarnRemindAmount,
    double? giveAmount,
    int? id,
    double? initCash,
    double? interestAmount,
    double? interestRate,
    String? marketType,
    int? multiple,
    double? negativeAmount,
    String? openTime,
    int? periodType,
    double? positionAmount,
    double? receivableInterest,
    int? settlementStatus,
    double? todayWinAmount,
    double? todayWinRate,
    int? totalAccountAmount,
    double? totalCash,
    double? totalFinance,
    double? totalPower,
    int? type,
    double? useAmount,
    double? warnRemindAmount,
    double? winRate,
    double? winAmount,
    double? withdrawAmount,
    int? yesterdayAsset,
    bool? isAutoRenew,
  }) {
    return ContractSummaryPageRecord()
      ..accountWinAmount = accountWinAmount ?? this.accountWinAmount
      ..allAsset = allAsset ?? this.allAsset
      ..closeRemindAmount = closeRemindAmount ?? this.closeRemindAmount
      ..contractAssetAmount = contractAssetAmount ?? this.contractAssetAmount
      ..coverLossAmount = coverLossAmount ?? this.coverLossAmount
      ..currency = currency ?? this.currency
      ..expendAmount = expendAmount ?? this.expendAmount
      ..expireTime = expireTime ?? this.expireTime
      ..freezePower = freezePower ?? this.freezePower
      ..gapCloseRemindAmount = gapCloseRemindAmount ?? this.gapCloseRemindAmount
      ..gapWarnRemindAmount = gapWarnRemindAmount ?? this.gapWarnRemindAmount
      ..giveAmount = giveAmount ?? this.giveAmount
      ..id = id ?? this.id
      ..initCash = initCash ?? this.initCash
      ..interestAmount = interestAmount ?? this.interestAmount
      ..interestRate = interestRate ?? this.interestRate
      ..marketType = marketType ?? this.marketType
      ..multiple = multiple ?? this.multiple
      ..negativeAmount = negativeAmount ?? this.negativeAmount
      ..openTime = openTime ?? this.openTime
      ..periodType = periodType ?? this.periodType
      ..positionAmount = positionAmount ?? this.positionAmount
      ..receivableInterest = receivableInterest ?? this.receivableInterest
      ..settlementStatus = settlementStatus ?? this.settlementStatus
      ..todayWinAmount = todayWinAmount ?? this.todayWinAmount
      ..todayWinRate = todayWinRate ?? this.todayWinRate
      ..totalAccountAmount = totalAccountAmount ?? this.totalAccountAmount
      ..totalCash = totalCash ?? this.totalCash
      ..totalFinance = totalFinance ?? this.totalFinance
      ..totalPower = totalPower ?? this.totalPower
      ..type = type ?? this.type
      ..useAmount = useAmount ?? this.useAmount
      ..warnRemindAmount = warnRemindAmount ?? this.warnRemindAmount
      ..winRate = winRate ?? this.winRate
      ..winAmount = winAmount ?? this.winAmount
      ..withdrawAmount = withdrawAmount ?? this.withdrawAmount
      ..yesterdayAsset = yesterdayAsset ?? this.yesterdayAsset
      ..isAutoRenew = isAutoRenew ?? this.isAutoRenew;
  }
}