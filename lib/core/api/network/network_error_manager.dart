import 'dart:async';

import 'package:dio/dio.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:gp_stock_app/config/flavors/app_config.dart';
import 'package:gp_stock_app/flavors.dart';
import 'package:gp_stock_app/shared/app/utilities/easy_loading.dart';
import 'package:gp_stock_app/shared/constants/enums.dart';
import 'package:gp_stock_app/shared/routes/navigator.dart';

import 'network_helper.dart';

class NetworkErrorManager {
  static final NetworkErrorManager _instance = NetworkErrorManager._internal();
  factory NetworkErrorManager() => _instance;
  NetworkErrorManager._internal();

  DateTime? _lastErrorTime;
  String? _lastErrorMessage;
  final int _errorDebounceSeconds = 10;
  int _errorCount = 0;
  bool _isShowingNetworkError = false;

  /// Shows a network error message if it hasn't been shown recently
  void showNetworkError({String? customMessage, DioException? error}) {
    final context = navigatorKey.currentContext;
    if (context == null) return;

    try {
      GPEasyLoading.dismiss();
    } catch (_) {}

    String errorMessage = _getErrorMessageForException(error) ?? customMessage ?? 'networkErrorMessage'.tr();

    String? endpoint;
    int? statusCode;

    if (error != null) {
      endpoint = error.requestOptions.path;
      statusCode = error.response?.statusCode;

      // Show endpoint information in debug mode or for GP flavor
      if (kDebugMode || F.appFlavor == Flavor.gp) {
        errorMessage = '$errorMessage\n\nEndpoint: $endpoint';
      }
    }

    if (_shouldShowError(errorMessage)) {
      _isShowingNetworkError = true;
      _lastErrorTime = DateTime.now();
      _lastErrorMessage = errorMessage;

      NetworkHelper.handleMessage(
        errorMessage,
        context,
        title: statusCode != null && (kDebugMode || F.appFlavor == Flavor.gp) ? '$statusCode' : 'Error',
        type: HandleTypes.customDialog,
        snackBarType: SnackBarType.error,
        dialogKey: 'network_error',
        onTap: () {
          Navigator.pop(context);
          _isShowingNetworkError = false;
        },
      );

      Future.delayed(const Duration(seconds: 10), () {
        _isShowingNetworkError = false;
      });
    }
  }

  /// Gets a user-friendly error message based on the exception type
  String? _getErrorMessageForException(DioException? error) {
    if (error == null) return null;

    switch (error.type) {
      case DioExceptionType.connectionTimeout:
        return 'networkErrorTimeout'.tr();
      case DioExceptionType.sendTimeout:
        return 'networkErrorSendTimeout'.tr();
      case DioExceptionType.receiveTimeout:
        return 'networkErrorReceiveTimeout'.tr();
      case DioExceptionType.badCertificate:
        return 'networkErrorCertificate'.tr();
      case DioExceptionType.badResponse:
        return _getMessageForStatusCode(error.response?.statusCode);
      case DioExceptionType.cancel:
        return 'networkErrorCancelled'.tr();
      case DioExceptionType.connectionError:
        return 'networkErrorConnection'.tr();
      case DioExceptionType.unknown:
        if (error.error != null) {
          final errorString = error.error.toString().toLowerCase();
          if (errorString.contains('socket')) return 'networkErrorSocket'.tr();
          if (errorString.contains('dns')) return 'networkErrorDns'.tr();
          if (errorString.contains('no host specified')) return 'networkErrorNoHost'.tr();
          if (errorString.contains('failed to parse')) return 'networkErrorParsing'.tr();
        }
        return 'networkErrorUnknown'.tr();
    }
  }

  /// Gets a user-friendly error message based on HTTP status code
  String _getMessageForStatusCode(int? statusCode) {
    switch (statusCode) {
      case 400:
        return 'networkErrorBadRequest'.tr();
      case 401:
        return 'networkErrorUnauthorized'.tr();
      case 403:
        return 'networkErrorForbidden'.tr();
      case 404:
        return 'networkErrorNotFound'.tr();
      case 500:
      case 501:
      case 502:
      case 503:
        return 'networkErrorServer'.tr();
      default:
        return 'networkErrorBadResponse'.tr();
    }
  }

  /// Determines if an error should be shown based on debounce rules
  bool _shouldShowError(String errorMessage) {
    if (_isShowingNetworkError) return false;

    if (_lastErrorTime == null || _lastErrorMessage != errorMessage) {
      _errorCount = 1;
      return true;
    }

    final now = DateTime.now();
    final difference = now.difference(_lastErrorTime!).inSeconds;

    if (_errorCount < 3) {
      _errorCount++;
      return difference > 2;
    }

    return difference > _errorDebounceSeconds;
  }

  /// Checks if the error is a network-related error
  bool isNetworkError(dynamic error) {
    if (error == null) return false;

    final errorString = error.toString().toLowerCase();

    return errorString.contains('socket') ||
        errorString.contains('connection') ||
        errorString.contains('network') ||
        errorString.contains('timeout') ||
        errorString.contains('host') ||
        errorString.contains('dns') ||
        errorString.contains('404 not found') ||
        errorString.contains('failed to parse') ||
        errorString.contains('websocket') ||
        errorString.contains('no host specified');
  }
}
