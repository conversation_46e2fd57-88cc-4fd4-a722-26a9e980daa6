#!/bin/bash

# switch_flavor.sh - A script to switch flavors in a Flutter app
# Usage: ./switch_flavor.sh [flavor_name]

# Check if flavor argument is provided
if [ -z "$1" ]; then
    echo "Error: Please provide a flavor name as an argument"
    echo "Usage: ./switch_flavor.sh [flavor_name]"
    echo "Available flavors: $(ls -1 assets/flavors | tr '\n' ' ')"
    exit 1
fi

FLAVOR="$1"
FLAVOR_DIR="assets/flavors/$FLAVOR"
TARGET_DIR="assets"
TRANSLATIONS_DIR="$TARGET_DIR/translations"
FLAVOR_TRANSLATIONS_DIR="$FLAVOR_DIR/translations"

# Check if flavor directory exists
if [ ! -d "$FLAVOR_DIR" ]; then
    echo "Error: Flavor '$FLAVOR' not found in assets/flavors/"
    echo "Available flavors: $(ls -1 assets/flavors | tr '\n' ' ')"
    exit 1
fi

echo "Switching to flavor: $FLAVOR"
echo "Copying files from $FLAVOR_DIR to $TARGET_DIR"

# Find all files in the flavor directory and copy them to the assets directory
find "$FLAVOR_DIR" -type f -not -path "*/translations/*" | while read -r file; do
    # Get the relative path within the flavor directory
    rel_path="${file#$FLAVOR_DIR/}"
    
    # Create target directory if it doesn't exist
    target_dir="$TARGET_DIR/$(dirname "$rel_path")"
    mkdir -p "$target_dir"
    
    # Copy the file (will replace if exists)
    cp -v "$file" "$TARGET_DIR/$rel_path"
done

# Handle translation files separately
echo "Updating translation files for flavor: $FLAVOR"

# Check if jq is installed (needed for JSON manipulation)
if ! command -v jq &> /dev/null; then
    echo "Error: 'jq' is required for JSON manipulation but it's not installed."
    echo "Please install jq using your package manager:"
    echo "  - For Ubuntu/Debian: sudo apt-get install jq"
    echo "  - For MacOS: brew install jq"
    echo "  - For Windows with Chocolatey: choco install jq"
    exit 1
fi

# Process each translation file
for lang_file in "en-US.json" "zh-CN.json" "zh-TW.json"; do
    # Path to the original translation file
    orig_file="$TRANSLATIONS_DIR/$lang_file"
    
    # Path to the flavor-specific translation overrides
    flavor_override_file="$FLAVOR_TRANSLATIONS_DIR/$lang_file"
    
    # Check if both files exist
    if [ -f "$orig_file" ] && [ -f "$flavor_override_file" ]; then
        echo "Updating $lang_file with $FLAVOR-specific translations"
        
        # Merge the flavor-specific translations into the original file
        # The flavor-specific translations will override any existing keys
        jq -s '.[0] * .[1]' "$orig_file" "$flavor_override_file" > "$orig_file.tmp"
        mv "$orig_file.tmp" "$orig_file"
    elif [ -f "$flavor_override_file" ]; then
        echo "Creating $lang_file with $FLAVOR-specific translations"
        mkdir -p "$TRANSLATIONS_DIR"
        cp "$flavor_override_file" "$orig_file"
    else
        echo "No flavor-specific translations found for $lang_file, skipping"
    fi
done

echo "Flavor switched to '$FLAVOR' successfully!"